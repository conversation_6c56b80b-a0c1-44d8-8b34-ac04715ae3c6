@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-ibm-plex-arabic);
  --font-arabic: var(--font-ibm-plex-arabic);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* White background with dark blue accents */
  --background: oklch(1 0 0); /* Pure white */
  --foreground: oklch(0.2 0.05 240); /* Dark blue text */
  --card: oklch(1 0 0); /* White cards */
  --card-foreground: oklch(0.2 0.05 240); /* Dark blue text on cards */
  --popover: oklch(1 0 0); /* White popovers */
  --popover-foreground: oklch(0.2 0.05 240); /* Dark blue text on popovers */
  --primary: oklch(0.25 0.08 240); /* Dark blue primary */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(0.95 0.01 240); /* Very light blue secondary */
  --secondary-foreground: oklch(0.2 0.05 240); /* Dark blue text on secondary */
  --muted: oklch(0.96 0.005 240); /* Very light blue muted */
  --muted-foreground: oklch(0.45 0.03 240); /* Medium blue muted text */
  --accent: oklch(0.3 0.06 240); /* Slightly lighter dark blue accent */
  --accent-foreground: oklch(1 0 0); /* White text on accent */
  --destructive: oklch(0.577 0.245 27.325); /* Keep red for destructive */
  --border: oklch(0.9 0.01 240); /* Light blue borders */
  --input: oklch(0.98 0.005 240); /* Very light blue inputs */
  --ring: oklch(0.3 0.06 240); /* Dark blue focus ring */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #002443;
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #002443;
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-ibm-plex-arabic), sans-serif;
    direction: rtl;
    overflow-x: hidden;
  }

  /* Arabic font utilities */
  .font-arabic {
    font-family: var(--font-ibm-plex-arabic), sans-serif;
  }

  /* RTL utilities */
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }
}

/* Global pointer cursor for all clickable elements */
button,
[role="button"],
[type="button"],
[type="submit"],
[type="reset"],
input[type="checkbox"],
input[type="radio"],
select,
a,
[href],
[onclick],
[tabindex]:not([tabindex="-1"]),
.cursor-pointer,
[data-clickable],
[data-radix-collection-item],
[data-radix-dropdown-menu-item],
[data-radix-select-item],
[data-radix-menubar-item],
[data-radix-context-menu-item],
[data-radix-navigation-menu-link],
[data-radix-accordion-trigger],
[data-radix-collapsible-trigger],
[data-radix-dialog-trigger],
[data-radix-popover-trigger],
[data-radix-tooltip-trigger],
[data-radix-hover-card-trigger],
[data-radix-alert-dialog-trigger],
[data-radix-sheet-trigger],
[data-radix-tabs-trigger],
[data-radix-toggle],
[data-radix-switch-thumb],
[data-radix-slider-thumb],
[data-radix-scroll-area-thumb],
[data-radix-menubar-trigger],
[data-radix-dropdown-menu-trigger],
[data-radix-context-menu-trigger],
[data-radix-select-trigger],
[data-radix-combobox-trigger],
[data-radix-date-picker-trigger],
[data-radix-calendar-day],
[data-radix-calendar-grid-cell],
[data-radix-pagination-link],
[data-radix-breadcrumb-link],
[data-radix-avatar],
[data-radix-badge],
.clickable,
.interactive,
.selectable {
  cursor: pointer !important;
}

/* Ensure disabled elements don't show pointer */
button:disabled,
[role="button"]:disabled,
[type="button"]:disabled,
[type="submit"]:disabled,
[type="reset"]:disabled,
input:disabled,
select:disabled,
[aria-disabled="true"],
[data-disabled="true"],
[data-state="disabled"],
.disabled {
  cursor: not-allowed !important;
}

/* Loading states should show default cursor */
[data-loading="true"],
.loading {
  cursor: wait !important;
}

/* Text selection areas should show text cursor */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="number"],
textarea,
[contenteditable="true"],
[role="textbox"] {
  cursor: text !important;
}

/* Resize handles */
[data-radix-scroll-area-scrollbar],
[data-radix-resizable-handle] {
  cursor: grab !important;
}

[data-radix-scroll-area-scrollbar]:active,
[data-radix-resizable-handle]:active {
  cursor: grabbing !important;
}

/* Custom component cursors */
.table-row-clickable,
.card-clickable,
.list-item-clickable,
.menu-item,
.dropdown-item,
.sidebar-item,
.nav-item,
.tab-item,
.accordion-item,
.collapsible-item,
.dialog-trigger,
.sheet-trigger,
.popover-trigger,
.tooltip-trigger,
.hover-card-trigger,
.alert-dialog-trigger,
.breadcrumb-item,
.pagination-item,
.calendar-day,
.date-picker-day,
.avatar-clickable,
.badge-clickable,
.chip-clickable,
.tag-clickable {
  cursor: pointer !important;
}

/* Table specific cursors */
table tr[onclick],
table tr[data-clickable],
table tr.clickable,
table tbody tr:hover,
.table-hover tr:hover {
  cursor: pointer !important;
}

/* Card specific cursors */
.card[onclick],
.card[data-clickable],
.card.clickable,
.card-hover:hover {
  cursor: pointer !important;
}

/* Icon buttons and icon-only elements */
.icon-button,
.icon-clickable,
[data-icon-button],
svg[onclick],
svg[data-clickable] {
  cursor: pointer !important;
}

/* Form elements that should show pointer */
label[for],
.form-label[for],
.checkbox-label,
.radio-label,
.switch-label {
  cursor: pointer !important;
}

/* Additional interactive elements */
.hover\:bg-muted\/50:hover,
.hover\:bg-accent:hover,
.hover\:bg-secondary:hover,
.hover\:opacity-80:hover,
.hover\:opacity-90:hover,
.hover\:opacity-100:hover,
.hover\:scale-105:hover,
.hover\:shadow-md:hover,
.hover\:shadow-lg:hover,
.transition-all,
.transition-colors,
.transition-opacity,
.transition-transform,
.transition-shadow {
  cursor: pointer !important;
}

/* Specific component cursors */
[data-slot="breadcrumb-link"],
[data-slot="sidebar-menu-button"],
[data-slot="sidebar-menu-action"],
[data-slot="dropdown-menu-item"],
[data-slot="select-item"],
[data-slot="dialog-trigger"],
[data-slot="sheet-trigger"],
[data-slot="alert-dialog-trigger"],
[data-slot="tooltip-trigger"],
[data-slot="popover-trigger"],
[data-slot="collapsible-trigger"],
[data-slot="accordion-trigger"] {
  cursor: pointer !important;
}

/* Avatar and profile elements */
.avatar,
.profile-avatar,
.user-avatar {
  cursor: pointer !important;
}

/* Navigation and menu items */
.nav-link,
.menu-link,
.sidebar-link,
.breadcrumb-link,
.pagination-link {
  cursor: pointer !important;
}

/* Enhanced smooth transitions */
@layer base {
  * {
    /* Removed transform from global transitions to prevent interference with Radix UI animations */
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* Exclude Radix UI dropdown/popover elements from global transitions */
  [data-radix-dropdown-menu-content],
  [data-radix-select-content],
  [data-radix-popover-content],
  [data-radix-tooltip-content],
  [data-radix-context-menu-content],
  [data-radix-menubar-content],
  [data-radix-navigation-menu-content],
  [data-radix-hover-card-content],
  [data-radix-alert-dialog-content],
  [data-radix-dialog-content],
  [data-radix-sheet-content] {
    transition: none !important;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced focus states */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    transition: outline-offset 150ms ease;
  }

  /* Smooth hover states for interactive elements - with specific transform transitions */
  button, a, [role="button"] {
    transition: color 200ms ease, background-color 200ms ease, border-color 200ms ease, opacity 200ms ease, box-shadow 200ms ease, transform 200ms ease;
  }

  button:hover:not(:disabled), a:hover, [role="button"]:hover:not([aria-disabled="true"]) {
    transform: translateY(-1px);
  }

  /* Ensure Radix UI triggers don't get the hover transform */
  [data-radix-dropdown-menu-trigger]:hover,
  [data-radix-select-trigger]:hover,
  [data-radix-popover-trigger]:hover,
  [data-radix-context-menu-trigger]:hover,
  [data-radix-menubar-trigger]:hover {
    transform: none !important;
  }

  /* Loading animation improvements */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(-25%);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
      transform: translateY(0);
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
  }

  /* Skeleton loading animation */
  @keyframes skeleton {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-skeleton {
    background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted-foreground) / 0.1) 50%, hsl(var(--muted)) 75%);
    background-size: 200px 100%;
    animation: skeleton 1.5s infinite linear;
  }

  /* Smooth sidebar transitions */
  [data-slot="sidebar"] {
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Page transition animations */
  .page-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms ease, transform 300ms ease;
  }

  .page-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .page-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 200ms ease, transform 200ms ease;
  }

  /* Custom Scrollbar Styles - Enhanced Design */
  /* For Webkit browsers (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.2);
    border-radius: 6px;
    margin: 4px;
    border: 1px solid hsl(var(--border) / 0.3);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.8) 0%,
      hsl(var(--primary) / 0.6) 50%,
      hsl(var(--primary) / 0.7) 100%);
    border-radius: 6px;
    border: 1px solid hsl(var(--primary) / 0.3);
    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px hsl(var(--primary) / 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.95) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.85) 100%);
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: 0 4px 8px hsl(var(--primary) / 0.2);
    transform: scaleX(1.1);
  }

  ::-webkit-scrollbar-thumb:active {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.7);
    box-shadow: 0 2px 4px hsl(var(--primary) / 0.3);
  }

  ::-webkit-scrollbar-corner {
    background: hsl(var(--muted) / 0.2);
    border-radius: 6px;
  }

  /* For Firefox */
  html {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary) / 0.7) hsl(var(--muted) / 0.2);
  }

  /* RTL Scrollbar positioning for the main content area */
  body {
    overflow-x: hidden;
    direction: rtl;
  }

  /* Custom scrollbar container for RTL layout */
  .rtl-scrollbar {
    direction: rtl;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* RTL-specific scrollbar positioning */
  html[dir="rtl"] body,
  html[dir="rtl"] .rtl-scrollbar {
    direction: rtl;
  }

  /* Enhanced RTL scrollbar container */
  .rtl-scrollbar-container {
    position: relative;
    direction: rtl;
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .rtl-scrollbar-content {
    direction: rtl;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    padding-left: 12px; /* Space for custom scrollbar */
    margin-left: -2px; /* Adjust for scrollbar positioning */
  }

  /* Force scrollbar to left side using CSS transforms for webkit */
  html[dir="rtl"] .rtl-scrollbar-content::-webkit-scrollbar {
    position: absolute;
    left: 0;
    right: auto;
  }

  /* Alternative approach: Use CSS to simulate left-side scrollbar */
  .custom-rtl-scrollbar {
    position: relative;
    overflow: hidden;
  }

  .custom-rtl-scrollbar::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 12px;
    height: 100%;
    background: hsl(var(--muted) / 0.1);
    border-radius: 6px;
    z-index: 1;
    pointer-events: none;
  }

  .rtl-scrollbar::-webkit-scrollbar {
    width: 10px;
  }

  .rtl-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.15);
    border-radius: 6px;
    margin: 6px 0;
    border: 1px solid hsl(var(--border) / 0.2);
  }

  .rtl-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.85) 0%,
      hsl(var(--primary) / 0.65) 50%,
      hsl(var(--primary) / 0.75) 100%);
    border-radius: 6px;
    border: 1px solid hsl(var(--primary) / 0.4);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 2px 4px hsl(var(--primary) / 0.1),
      inset 0 1px 0 hsl(var(--primary) / 0.2);
  }

  .rtl-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.95) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.9) 100%);
    border-color: hsl(var(--primary) / 0.6);
    transform: scaleX(1.15);
    box-shadow:
      0 4px 8px hsl(var(--primary) / 0.2),
      inset 0 1px 0 hsl(var(--primary) / 0.3);
  }

  .rtl-scrollbar::-webkit-scrollbar-thumb:active {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.8);
    transform: scaleX(1.05);
    box-shadow:
      0 2px 4px hsl(var(--primary) / 0.3),
      inset 0 1px 0 hsl(var(--primary) / 0.4);
  }

  /* Dark mode scrollbar adjustments */
  .dark ::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.2);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.7);
    border: 1px solid hsl(var(--border));
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.9);
  }

  .dark .rtl-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, hsl(var(--primary) / 0.8), hsl(var(--primary) / 0.6));
  }

  .dark .rtl-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  }

  /* Smooth scrolling behavior */
  .rtl-scrollbar {
    scroll-behavior: smooth;
  }

  /* Hide scrollbar for specific elements when needed */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Custom scrollbar for sidebar content */
  [data-radix-scroll-area-viewport] {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--sidebar-primary) / 0.6) transparent;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar {
    width: 6px;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
    background: transparent;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
    background: hsl(var(--sidebar-primary) / 0.4);
    border-radius: 3px;
    transition: background-color 200ms ease;
  }

  [data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--sidebar-primary) / 0.6);
  }

  /* RTL Scrollbar enabled body styles */
  body.rtl-scrollbar-enabled {
    overflow-x: hidden;
  }

  body.rtl-scrollbar-enabled .rtl-scrollbar-container {
    position: relative;
    direction: rtl;
  }

  body.rtl-scrollbar-enabled .rtl-scrollbar-content {
    direction: rtl;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary) / 0.7) hsl(var(--muted) / 0.2);
  }

  /* Enhanced scrollbar for RTL content */
  .rtl-scrollbar-content::-webkit-scrollbar {
    width: 12px;
  }

  .rtl-scrollbar-content::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.1);
    border-radius: 8px;
    margin: 8px 0;
    border: 1px solid hsl(var(--border) / 0.1);
  }

  .rtl-scrollbar-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.9) 0%,
      hsl(var(--primary) / 0.7) 50%,
      hsl(var(--primary) / 0.8) 100%);
    border-radius: 8px;
    border: 2px solid hsl(var(--background));
    box-shadow:
      0 2px 6px hsl(var(--primary) / 0.15),
      inset 0 1px 0 hsl(var(--primary) / 0.3),
      inset 0 -1px 0 hsl(var(--primary) / 0.1);
    transition: all 350ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .rtl-scrollbar-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary) / 0.85) 50%,
      hsl(var(--primary) / 0.95) 100%);
    border-color: hsl(var(--background));
    transform: scaleX(1.2);
    box-shadow:
      0 4px 12px hsl(var(--primary) / 0.25),
      inset 0 1px 0 hsl(var(--primary) / 0.4),
      inset 0 -1px 0 hsl(var(--primary) / 0.2);
  }

  .rtl-scrollbar-content::-webkit-scrollbar-thumb:active {
    background: hsl(var(--primary));
    transform: scaleX(1.1);
    box-shadow:
      0 2px 6px hsl(var(--primary) / 0.3),
      inset 0 1px 0 hsl(var(--primary) / 0.5);
  }

  /* Dark mode enhancements for RTL scrollbar */
  .dark .rtl-scrollbar-content::-webkit-scrollbar-track {
    background: hsl(var(--muted) / 0.05);
    border-color: hsl(var(--border) / 0.05);
  }

  .dark .rtl-scrollbar-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.8) 0%,
      hsl(var(--primary) / 0.6) 50%,
      hsl(var(--primary) / 0.7) 100%);
    border-color: hsl(var(--background));
  }

  .dark .rtl-scrollbar-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
      hsl(var(--primary) / 0.95) 0%,
      hsl(var(--primary) / 0.8) 50%,
      hsl(var(--primary) / 0.9) 100%);
  }
}

/* Modern Loading Animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

/* Enhanced bounce animation for dots */
@keyframes bounce-smooth {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.animate-bounce-smooth {
  animation: bounce-smooth 1.4s infinite ease-in-out;
}

/* Enhanced Sidebar Menu Button Hover and Active Effects */
[data-sidebar="menu-button"] {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

[data-sidebar="menu-button"]:hover {
  background-color: #002443 !important;
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

[data-sidebar="menu-button"]:hover svg {
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

[data-sidebar="menu-button"][data-active="true"] {
  background-color: #002443 !important;
  color: hsl(var(--sidebar-accent-foreground)) !important;
  font-weight: 600;
  border-right: 3px solid hsl(var(--sidebar-primary));
}

[data-sidebar="menu-button"][data-active="true"] svg {
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

/* Enhanced Sidebar Sub Menu Button Effects */
[data-sidebar="menu-sub-button"] {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

[data-sidebar="menu-sub-button"]:hover {
  background-color: rgba(0, 36, 67, 0.7) !important;
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

[data-sidebar="menu-sub-button"][data-active="true"] {
  background-color: rgba(0, 36, 67, 0.8) !important;
  color: hsl(var(--sidebar-accent-foreground)) !important;
  font-weight: 600;
  border-right: 2px solid hsl(var(--sidebar-primary));
}

/* Collapsible trigger enhanced effects */
[data-sidebar="menu-button"] .lucide-chevron-left {
  transition: all 0.2s ease-in-out;
}

[data-sidebar="menu-button"]:hover .lucide-chevron-left {
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

/* Profile dropdown trigger enhanced effects */
[data-sidebar="menu-button"][data-state="open"] {
  background-color: #002443 !important;
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

[data-sidebar="menu-button"][data-state="open"] svg {
  color: hsl(var(--sidebar-accent-foreground)) !important;
}

/* Profile Dropdown Menu Item Effects */
[data-radix-dropdown-menu-item] {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

[data-radix-dropdown-menu-item]:hover {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-item]:hover svg {
  color: white !important;
}

[data-radix-dropdown-menu-item]:hover span {
  color: white !important;
}

[data-radix-dropdown-menu-item]:focus {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-item]:focus svg {
  color: white !important;
}

[data-radix-dropdown-menu-item]:focus span {
  color: white !important;
}

/* Additional selectors for dropdown menu items */
[data-radix-dropdown-menu-content] [role="menuitem"]:hover {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:hover svg {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:hover span {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:focus {
  background-color: #002443 !important;
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:focus svg {
  color: white !important;
}

[data-radix-dropdown-menu-content] [role="menuitem"]:focus span {
  color: white !important;
}
